<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <repositories>
        <repository>
            <id>ruima-nexus</id>
            <name>RuimaNexus</name>
            <url>http://59.172.92.78:8081/repository/maven_ruima</url>
        </repository>
    </repositories>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.4</version>
        <relativePath/>
    </parent>

    <groupId>net.airuima.ksw.plugin</groupId>
    <artifactId>mom-ksw-plugin</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>mom-ksw-plugin</name>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <profiles>
        <profile>
            <id>base</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>mom</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>rbase</artifactId>
                    <version>1.8.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima.facility.base</groupId>
                    <artifactId>facilitybase</artifactId>
                    <version>1.8.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>foundry</artifactId>
                    <version>1.8.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>bom</artifactId>
                    <version>1.8.0-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-shade-plugin</artifactId>
                        <version>3.3.0</version>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>shade</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>net.airuima</groupId>
                        <artifactId>ruima-encrypt-plugin</artifactId>
                        <version>1.0.0-SNAPSHOT</version>
                        <configuration>
                            <target>${project.basedir}/target</target>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>encrypt-jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>debug</id>
            <dependencies>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>mom</artifactId>
                    <version>0.0.1-SNAPSHOT</version>
                </dependency>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>rbase</artifactId>
                    <version>1.8.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima.facility.base</groupId>
                    <artifactId>facilitybase</artifactId>
                    <version>1.8.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>foundry</artifactId>
                    <version>1.8.1-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>bom</artifactId>
                    <version>1.8.0-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

</project>
