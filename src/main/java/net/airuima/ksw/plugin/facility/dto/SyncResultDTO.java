package net.airuima.ksw.plugin.facility.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-06-02
 */
@Schema(description = "同步结果信息DTO")
public class SyncResultDTO {
    /**
     *  表的主键ID
     */
    @Schema(description = "对接数据ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 结果返回状态
     */
    @Schema(description = "处理状态(0:未处理;1:处理成功;2:处理失败)")
    private Integer status;

    /**
     * 结果返回消息
     */
    @Schema(description = "异常提醒信息")
    private String message;

    public SyncResultDTO() {
    }

    public SyncResultDTO(Integer status, String message){
        this.status = status;
        this.message = message;
    }

    public SyncResultDTO(Long id, Integer status, String message){
        this.id = id;
        this.status = status;
        this.message = message;
    }

    public Long getId() {
        return id;
    }

    public SyncResultDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public SyncResultDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public SyncResultDTO setMessage(String message) {
        this.message = message;
        return this;
    }
}
