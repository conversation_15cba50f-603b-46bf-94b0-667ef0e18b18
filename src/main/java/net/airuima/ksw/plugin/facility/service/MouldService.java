package net.airuima.ksw.plugin.facility.service;

import net.airuima.constant.Constants;
import net.airuima.facility.base.domain.base.facility.Facility;
import net.airuima.facility.base.repository.base.facility.FacilityRepository;
import net.airuima.ksw.plugin.facility.dto.SyncMaterialDTO;
import net.airuima.ksw.plugin.facility.dto.SyncResultDTO;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模具对接service
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class MouldService {

    @Autowired
    private FacilityRepository facilityRepository;

    public List<SyncResultDTO> syncMoulds(List<SyncMaterialDTO> syncMaterialDtoList) {

        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();

        List<String> materialCodes = syncMaterialDtoList.stream().filter(syncMaterialDto -> ValidateUtils.isValid(syncMaterialDto.getCode()))
                .map(SyncMaterialDTO::getCode).distinct().collect(Collectors.toList());

        List<Facility> moulds = facilityRepository.findByCodeInAndDeleted(materialCodes, Constants.LONG_ZERO);
        Map<String, Facility> mouldCodeMap = moulds.stream().collect(Collectors.toMap(Facility::getCode, facility -> facility));
        syncMaterialDtoList.forEach(syncMaterialDto -> {

            Facility facility = mouldCodeMap.get(syncMaterialDto.getCode());
            //新增
            if (syncMaterialDto.getOperate() == Constants.INT_ZERO){

                if (facility != null){
                    syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(),Constants.INT_TWO,syncMaterialDto.getCode()+"已存在重复新增"));
                    return;
                }
                //默认为新增为模具型号：category：1，type：2，status：1（闲置）
                Facility mould = new Facility();
                mould.setCode(syncMaterialDto.getCode()).setName(syncMaterialDto.getName())
                        .setSpecification(syncMaterialDto.getSpecification())
                        .setIsEnable(Boolean.TRUE).setCategory(Constants.INT_ONE).setStatus(Constants.INT_ONE)
                        .setType(Constants.INT_TWO).setParent(new Facility(1938112029562765312L));
                facilityRepository.save(mould);
                syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(),Constants.INT_ONE,""));
                return;
            }
            //修改
            if (syncMaterialDto.getOperate() == Constants.INT_ONE){
                if (facility == null){
                    //设备不存在直接新增：按新增逻辑处理
                    //默认为新增为模具型号：category：1，type：2，status：1（闲置）
                    Facility mould = new Facility();
                    mould.setCode(syncMaterialDto.getCode()).setName(syncMaterialDto.getName())
                            .setSpecification(syncMaterialDto.getSpecification())
                            .setIsEnable(Boolean.TRUE).setCategory(Constants.INT_ONE).setStatus(Constants.INT_ONE)
                            .setType(Constants.INT_TWO).setParent(new Facility(1938112029562765312L));
                    facilityRepository.save(mould);
                    syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(),Constants.INT_ONE,""));
                    return;
                }
                //只能修改名称，以及规格型号
                facility.setName(syncMaterialDto.getName()).setSpecification(syncMaterialDto.getSpecification());
                facilityRepository.save(facility);
            }
            //删除
            if (syncMaterialDto.getOperate() == Constants.INT_TWO){
                if (facility == null){
                    syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(),Constants.INT_TWO,syncMaterialDto.getCode()+"模具，不存在无法删除"));
                    return;
                }
                facilityRepository.logicDelete(facility.getId());
                syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(),Constants.INT_ONE,""));
                return;
            }
            syncResultDtoList.add(new SyncResultDTO(syncMaterialDto.getId(),Constants.INT_TWO,syncMaterialDto.getCode()+"未匹配到对应的同步操作"));
        });
        return syncResultDtoList;
    }
}
