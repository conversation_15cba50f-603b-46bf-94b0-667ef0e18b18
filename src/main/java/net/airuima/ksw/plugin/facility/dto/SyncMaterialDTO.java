package net.airuima.ksw.plugin.facility.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright(C), 2017-2023,武汉睿码智能科技有限公司
 * 相关基础数据 MaterialDTO
 *
 * @author: rain
 * @date: 2023/11/13 13:56
 */
@Schema(name = "物料基础信息", description = "物料基础信息")
public class SyncMaterialDTO {

    /**
     * 主键ID
     */
    @Schema(description = "物料ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String name;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String code;

    /**
     * 主辅类型(1:主料;0:辅料)
     */
    @Schema(description = "主辅类型(1:主料;0:辅料)")
    private Integer materialCategory;

    /**
     * 物料组别(0:原材料;1:半成品;2:成品)
     */
    @Schema(description = "物料组别(0:原材料;1:半成品;2:成品)")
    private Integer materialGroup;

    /**
     * 规格型号
     */
    @Schema(description = "规格型号")
    private String specification;

    /**
     * 单位
     */
    @Schema(description = "单位")
    private String unit;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    private String parentPedigreeName;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    private String parentPedigreeCode;

    /**
     * 同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)
     */
    @Schema(description = "同步类型(0:新增;1:修改;2:删除;3:禁用;4:启用)")
    private Integer operate;

    public SyncMaterialDTO() {
    }

    public String getParentPedigreeName() {
        return parentPedigreeName;
    }

    public SyncMaterialDTO setParentPedigreeName(String parentPedigreeName) {
        this.parentPedigreeName = parentPedigreeName;
        return this;
    }

    public String getParentPedigreeCode() {
        return parentPedigreeCode;
    }

    public SyncMaterialDTO setParentPedigreeCode(String parentPedigreeCode) {
        this.parentPedigreeCode = parentPedigreeCode;
        return this;
    }

    public Long getId() {
        return id;
    }

    public SyncMaterialDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public SyncMaterialDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public SyncMaterialDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getMaterialCategory() {
        return materialCategory;
    }

    public SyncMaterialDTO setMaterialCategory(Integer materialCategory) {
        this.materialCategory = materialCategory;
        return this;
    }

    public Integer getMaterialGroup() {
        return materialGroup;
    }

    public SyncMaterialDTO setMaterialGroup(Integer materialGroup) {
        this.materialGroup = materialGroup;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public SyncMaterialDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public SyncMaterialDTO setUnit(String unit) {
        this.unit = unit;
        return this;
    }

    public Integer getOperate() {
        return operate;
    }

    public SyncMaterialDTO setOperate(Integer operate) {
        this.operate = operate;
        return this;
    }

    @Override
    public String toString() {
        return "MaterialDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", materialCategory=" + materialCategory +
                ", materialGroup=" + materialGroup +
                ", specification='" + specification + '\'' +
                ", unit='" + unit + '\'' +
                '}';
    }
}
