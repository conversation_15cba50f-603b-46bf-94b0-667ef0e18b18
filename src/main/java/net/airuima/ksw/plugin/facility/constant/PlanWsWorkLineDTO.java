package net.airuima.ksw.plugin.facility.constant;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 计划排产下单默认初始化生产线
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(description = "计划排产下单默认初始化生产线")
public class PlanWsWorkLineDTO implements Serializable {

    /**
     * 产品种类产品谱系编码
     */
    @Schema(description = "产品种类产品谱系编码")
    private String parentCode;

    /**
     * 生产线编码
     */
    @Schema(description = "生产线编码")
    private String workLineCode;

    public String getParentCode() {
        return parentCode;
    }

    public PlanWsWorkLineDTO setParentCode(String parentCode) {
        this.parentCode = parentCode;
        return this;
    }

    public String getWorkLineCode() {
        return workLineCode;
    }

    public PlanWsWorkLineDTO setWorkLineCode(String workLineCode) {
        this.workLineCode = workLineCode;
        return this;
    }
}
