package net.airuima.ksw.plugin.facility.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.facility.base.constant.Constants;
import net.airuima.facility.base.domain.base.facility.Facility;
import net.airuima.facility.base.dto.ErrorExportDTO;
import net.airuima.facility.base.dto.bom.MeteringUnitDTO;
import net.airuima.facility.base.dto.facility.FacilityDTO;
import net.airuima.facility.base.dto.organization.ClientDTO;
import net.airuima.facility.base.dto.organization.OrganizationDTO;
import net.airuima.facility.base.dto.rwms.WarehouseDTO;
import net.airuima.facility.base.enmus.AssetsCategory;
import net.airuima.facility.base.enmus.AssetsStatus;
import net.airuima.facility.base.enmus.MoldLevel;
import net.airuima.facility.base.proxy.bom.FacilityBaseMaterialUnitProxy;
import net.airuima.facility.base.proxy.organization.FacilityBaseClientProxy;
import net.airuima.facility.base.proxy.organization.FacilityBaseOrganizationProxy;
import net.airuima.facility.base.proxy.rwms.FacilityBaseWarehouseProxy;
import net.airuima.facility.base.repository.base.facility.FacilityRepository;
import net.airuima.facility.base.service.base.facility.FacilityService;
import net.airuima.facility.base.service.base.facility.api.IFacilityService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备基础模块扩展
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(-1)
public class FacilityServiceAdaptImpl implements IFacilityService {

    @Autowired
    private FacilityRepository facilityRepository;
    @Autowired
    private FacilityBaseClientProxy facilityBaseClientProxy;

    @Autowired
    private FacilityBaseWarehouseProxy facilityBaseWarehouseProxy;

    @Autowired
    private FacilityBaseMaterialUnitProxy facilityBaseMaterialUnitProxy;
    @Autowired
    private FacilityBaseOrganizationProxy facilityBaseOrganizationProxy;


    @Override
    public Page<Facility> findSpecPage(Specification<Facility> spec, Pageable pageable) {

        

    }

    @Override
    public void importTableExcel(MultipartFile file, HttpServletResponse response) throws Exception {
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(1);
        List<FacilityDTO> data = ExcelImportUtil.importExcel(file.getInputStream(), FacilityDTO.class, params);
        List<ClientDTO> clientDTOList = facilityBaseClientProxy.findByCodeIn(data.stream().map(FacilityDTO::getClient).toList());
        Map<String, ClientDTO> clientDTOMap = clientDTOList.stream().collect(Collectors.toMap(ClientDTO::getCode, Function.identity()));
        List<String> parentList = data.stream().map(FacilityDTO::getParent).toList();
        List<String> codeList = new ArrayList<>(data.stream().map(FacilityDTO::getCode).toList());
        codeList.addAll(parentList);
        List<Facility> facilityList = facilityRepository.findByCodeInAndIsEnableAndDeleted(codeList, Boolean.TRUE, Constants.LONG_ZERO);
        Map<String, Facility> facilityMap = facilityList.stream().collect(Collectors.toMap(Facility::getCode, Function.identity()));
        List<WarehouseDTO> warehouseDTOList = facilityBaseWarehouseProxy.findByCodeInAndDeleted(data.stream().filter(facilityDTO -> StringUtils.isNotBlank(facilityDTO.getWarehouse())).flatMap(facilityDTO -> Arrays.stream(facilityDTO.getWarehouse().split(","))).collect(Collectors.toList()), Constants.LONG_ZERO);
        Map<String, WarehouseDTO> warehouseDTOMap = warehouseDTOList.stream().collect(Collectors.toMap(WarehouseDTO::getCode, Function.identity()));
        List<MeteringUnitDTO> meteringUnitDTOList = facilityBaseMaterialUnitProxy.findByCodeInAndDeleted(data.stream().map(FacilityDTO::getMeteringUnit).toList(), Constants.LONG_ZERO);
        Map<String, MeteringUnitDTO> meteringUnitDTOMap = meteringUnitDTOList.stream().collect(Collectors.toMap(MeteringUnitDTO::getCode, Function.identity()));
        List<OrganizationDTO> organizationDTOList = facilityBaseOrganizationProxy.findByCodeInAndDeleted(data.stream().map(FacilityDTO::getOrganization).toList(), Constants.LONG_ZERO);
        Map<String, OrganizationDTO> organizationDTOMap = organizationDTOList.stream().collect(Collectors.toMap(OrganizationDTO::getCode, Function.identity()));
        List<String> errorMessage = new ArrayList<>();
        List<Facility> facilities = new ArrayList<>();
        for (FacilityDTO facilityDTO : data) {
            if (StringUtils.isBlank(facilityDTO.getCode())) {
                continue;
            }
            if (facilityMap.get(facilityDTO.getCode()) != null) {
                errorMessage.add("资产编码" + facilityDTO.getCode() + "重复");
                continue;
            }
            if (StringUtils.isBlank(facilityDTO.getName())) {
                if (StringUtils.isBlank(facilityDTO.getSpecification())) {
                    facilityDTO.setName(facilityDTO.getCode());
                } else {
                    facilityDTO.setName(facilityDTO.getSpecification());
                }
            }
            if (facilityMap.get(facilityDTO.getParent()) == null) {
                errorMessage.add("资产编码父级不存在" + facilityDTO.getCode());
                continue;
            }
            if (organizationDTOMap.get(facilityDTO.getOrganization()) == null) {
                errorMessage.add("使用部门不存在" + facilityDTO.getCode());
                continue;
            }
            if (meteringUnitDTOMap.get(facilityDTO.getMeteringUnit()) == null) {
                errorMessage.add("计量单位不存在" + facilityDTO.getCode());
                continue;
            }
            if (StringUtils.isBlank(facilityDTO.getWarehouse())) {
                errorMessage.add("存放区域不存在" + facilityDTO.getCode());
                continue;
            }
            if (clientDTOMap.get(facilityDTO.getClient()) == null) {
                errorMessage.add("关联客户不存在" + facilityDTO.getCode());
                continue;
            }
            Facility facility = new Facility().setCode(facilityDTO.getCode()).setName(facilityDTO.getName()).setParent(facilityMap.get(facilityDTO.getParent())).setSpecification(facilityDTO.getSpecification()).setOrganizationId(organizationDTOMap.get(facilityDTO.getOrganization()).getId()).setMeteringUnitId(meteringUnitDTOMap.get(facilityDTO.getMeteringUnit()).getId()).setClientId(clientDTOMap.get(facilityDTO.getClient()).getId()).setType(MoldLevel.DATA.get(MoldLevel.END_LEVEL)).setIsEnable(Boolean.TRUE).setCategory(AssetsCategory.DATA.get(AssetsCategory.MOLD)).setNumber(Constants.INT_ONE).setStatus(AssetsStatus.DATA.get(facilityDTO.getStatus()));
            List<String> idList = new ArrayList<>();
            for (String warehouse : facilityDTO.getWarehouse().split(",")) {
                WarehouseDTO warehouseDTO = warehouseDTOMap.get(warehouse);
                if (warehouseDTO != null) {
                    idList.add(warehouseDTO.getId().toString());
                }
            }
            if (!idList.isEmpty()) {
                facility.setStorageAreaId(String.join(",", idList));
            }
            facility.setCustom1(facilityDTO.getCompose());
            facility.setCustom2(facilityDTO.getNote());
            facilities.add(facility);
        }
        facilityRepository.saveAll(facilities);
        List<ErrorExportDTO> list = errorMessage.stream().map(ErrorExportDTO::new).collect(Collectors.toList());
        if (!list.isEmpty()) {
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setFreezeCol(net.airuima.constant.Constants.INT_TWO);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ErrorExportDTO.class, list);
            response.setContentType(file.getOriginalFilename().contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getOriginalFilename(), StandardCharsets.UTF_8));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            workbook.write(response.getOutputStream());
        }
    }
}
