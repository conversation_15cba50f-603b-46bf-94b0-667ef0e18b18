package net.airuima.ksw.plugin.facility.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import net.airuima.bom.domain.Bom;
import net.airuima.bom.domain.BomInfo;
import net.airuima.bom.domain.Material;
import net.airuima.bom.repository.BomInfoRepository;
import net.airuima.bom.repository.BomRepository;
import net.airuima.constant.Constants;
import net.airuima.foundry.service.procedure.aps.FoundryPreDistributionService;
import net.airuima.foundry.service.procedure.aps.api.IFoundryPreDistributionService;
import net.airuima.foundry.web.rest.procedure.aps.dto.FurnaceBatchWsDTO;
import net.airuima.ksw.plugin.facility.constant.PlanWsWorkFlowDTO;
import net.airuima.ksw.plugin.facility.constant.PlanWsWorkLineDTO;
import net.airuima.rbase.domain.base.pedigree.Pedigree;
import net.airuima.rbase.domain.base.process.WorkFlow;
import net.airuima.rbase.domain.base.scene.WorkLine;
import net.airuima.rbase.domain.procedure.aps.CascadeWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.dto.base.BaseDTO;
import net.airuima.rbase.dto.bom.BomDTO;
import net.airuima.rbase.dto.process.StepDTO;
import net.airuima.rbase.dto.rule.DictionaryDTO;
import net.airuima.rbase.proxy.bom.RbaseBomProxy;
import net.airuima.rbase.proxy.rule.RbaseDictionaryProxy;
import net.airuima.rbase.repository.base.pedigree.PedigreeRepository;
import net.airuima.rbase.repository.base.process.WorkFlowRepository;
import net.airuima.rbase.repository.base.scene.WorkLineRepository;
import net.airuima.rbase.repository.procedure.aps.CascadeWorkSheetRepository;
import net.airuima.rbase.repository.procedure.aps.WorkSheetRepository;
import net.airuima.rbase.service.procedure.aps.WorkSheetService;
import net.airuima.rbase.service.procedure.material.IWsMaterialService;
import net.airuima.rbase.util.MapperUtils;
import net.airuima.util.BeanUtil;
import net.airuima.util.RedisUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

/**
 * 工单关联熔炉批次相关接口实现
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(-1)
public class FoundryPreDistributionAdaptService implements IFoundryPreDistributionService {

    @Autowired
    private BomInfoRepository bomInfoRepository;
    @Autowired
    private RbaseBomProxy rbaseBomProxy;
    @Autowired
    private BomRepository bomRepository;
    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private CascadeWorkSheetRepository cascadeWorkSheetRepository;
    @Autowired
    private RbaseDictionaryProxy rbaseDictionaryProxy;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private WorkLineRepository workLineRepository;
    @Autowired
    private WorkSheetService workSheetService;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public List<WorkSheet> orderIssued(List<FurnaceBatchWsDTO> furnaceBatchWsDtoList) {

        //获取销售订单下单生成的工单列表
        List<WorkSheet> workSheets = BeanUtil.getBean(FoundryPreDistributionService.class)
                .orderIssued(furnaceBatchWsDtoList);

        Map<Long, BomInfo> bomInfoMap = new HashMap<>();

        //通过工单生成的列表，将产品代码 为cp开头的工单进行级联下单 zz，如果不存在bom
        //直接生成bom
        if (ValidateUtils.isValid(workSheets)) {
            workSheets.forEach(workSheet -> {

                Pedigree pedigree = workSheet.getPedigree();
                Long bomInfoId = workSheet.getBomInfoId();

                if (pedigree.getCode().startsWith("CP")) {
                    //工单投料单
                    List<BomDTO> bomDtoList = rbaseBomProxy.findByBomInfoId(bomInfoId);

                    if (ValidateUtils.isValid(bomDtoList)) {
                        Optional<BomDTO> bomDTOOptional = bomDtoList.stream()
                                .filter(bomDTO -> bomDTO.getChildMaterial().getCode().startsWith("ZZ"))
                                .findFirst();

                        if (bomDTOOptional.isPresent()) {
                            BomDTO bomDto = bomDTOOptional.get();
                            BomInfo bomInfo = bomInfoMap.get(bomDto.getChildMaterial().getId());
                            List<BomDTO> bomDtoList1 = Lists.newArrayList();
                            if (bomInfo == null) {
                                List<BomInfo> bomInfos = bomInfoRepository.findByMaterialIdAndIsEnableAndDeleted(bomDto.getChildMaterial().getId(), Boolean.TRUE, Constants.LONG_ZERO);
                                if (ValidateUtils.isValid(bomInfos)) {
                                    bomInfo = bomInfos.get(0);
                                    bomDtoList1 = rbaseBomProxy.findByBomInfoId(bomInfo.getId());
                                } else {
                                    bomInfo = new BomInfo();
                                    bomInfo.setMaterial(new Material(bomDto.getChildMaterial().getId()))
                                            .setName(bomDto.getChildMaterial().getName())
                                            .setCode(bomDto.getChildMaterial().getCode())
                                            .setIsEnable(Boolean.TRUE)
                                            .setDeleted(Constants.LONG_ZERO);
                                    bomInfo = bomInfoRepository.save(bomInfo);

                                    //bom
                                    Bom bom = new Bom();
                                    bom.setBomInfo(bomInfo)
                                            .setChildMaterial(new Material(bomDto.getChildMaterial().getId()))
                                            .setMaterial(new Material(bomDto.getChildMaterial().getId()))
                                            .setBackFlush(bomDto.getBackFlush())
                                            .setProportion(bomDto.getProportion())
                                            .setWastage(bomDto.getWastage())
                                            .setCategory(bomDto.getCategory())
                                            .setBase(bomDto.getBase())
                                            .setDeleted(Constants.LONG_ZERO);
                                    bomRepository.save(bom);
                                    bomDtoList1.add(MapperUtils.map(bom, BomDTO.class));
                                }
                                bomInfoMap.put(bomDto.getChildMaterial().getId(),bomInfo);
                            }else {
                                bomDtoList1 = rbaseBomProxy.findByBomInfoId(bomInfo.getId());
                            }


                            Pedigree childPedigree = pedigreeRepository.findByMaterialIdAndDeleted(bomDto.getChildMaterial().getId(), Constants.LONG_ZERO)
                                    .orElseThrow(() -> new ResponseException("childPedigreeEmpty", "未找到对应的谱系信息"));

                            WorkSheet childWorkSheet = new WorkSheet();
                            childWorkSheet.setSerialNumber("ZZ" + "-" + workSheet.getSerialNumber());
                            childWorkSheet.setNumber(workSheet.getNumber())
                                    .setCategory(Constants.INT_ONE)
                                    .setStatus(Constants.INT_ZERO)
                                    .setBomInfoId(bomInfo.getId())
                                    .setPedigree(childPedigree)
                                    .setGenerateSubWsStatus(Constants.INT_ZERO)
                                    .setDownGradeNumber(Constants.INT_ZERO);
                            childWorkSheet = workSheetRepository.save(childWorkSheet);

                            //保存投料单
                            wsMaterialServices[0].saveWsMaterial(childWorkSheet, bomDtoList1);

                            CascadeWorkSheet cascadeWorkSheet = new CascadeWorkSheet(workSheet, childWorkSheet);
                            cascadeWorkSheet.setDeleted(Constants.LONG_ZERO);
                            cascadeWorkSheetRepository.save(cascadeWorkSheet);
                        }
                    }

                }
            });
        }

        //添加 工艺路线，生产线，组织架构
        extendWsData(workSheets);
        //生产工单绑定的sn码
        bindWorkSheetSn(workSheets);
        return workSheets;
    }


    public void extendWsData(List<WorkSheet> workSheets){

        DictionaryDTO keyPlanWsWorkFlow = rbaseDictionaryProxy.findByCodeAndDeleted("key_plan_ws_work_flow", Constants.LONG_ZERO)
                .orElse(null);

        DictionaryDTO keyPlanWsWorkLine = rbaseDictionaryProxy.findByCodeAndDeleted("key_plan_ws_work_line", Constants.LONG_ZERO)
                .orElse(null);

        if (keyPlanWsWorkFlow == null || keyPlanWsWorkLine == null) {
            return;
        }
        List<PlanWsWorkFlowDTO> planWsWorkFlowDtoList = JSON.parseObject(keyPlanWsWorkFlow.getData(), new TypeReference<List<PlanWsWorkFlowDTO>>() {
        });
        List<PlanWsWorkLineDTO> planWsWorkLineDTOS = JSON.parseObject(keyPlanWsWorkLine.getData(), new TypeReference<List<PlanWsWorkLineDTO>>() {
        });

        workSheets.forEach(workSheet -> {
            workSheet = wsAddWorkFlowAndWorkLine(planWsWorkFlowDtoList,planWsWorkLineDTOS,workSheet);
            if (Objects.isNull(workSheet.getWorkFlow()) || Objects.isNull(workSheet.getWorkLine())) {
                return;
            }
            List<StepDTO> stepDtoList = Lists.newArrayList();
            // 验证工单工艺路线
            BaseDTO baseDto = workSheetService.validateWorkSheetExcelStepInfo(workSheet, stepDtoList);
            if (baseDto.getStatus().equals(Constants.KO)) {
             return;
            }
            //保存工序快照
            workSheetService.saveExcelWsSteps(Boolean.FALSE,workSheet, workSheet.getWorkFlow(), stepDtoList);
        });
    }

    public WorkSheet wsAddWorkFlowAndWorkLine(List<PlanWsWorkFlowDTO> planWsWorkFlowDtoList,List<PlanWsWorkLineDTO> planWsWorkLineDTOS, WorkSheet workSheet){

        if (workSheet.getPedigree().getCode().startsWith("ZZ")) {

            planWsWorkFlowDtoList.stream().filter(dto -> dto.getParentCode().equals("ZZ"))
                    .findFirst().ifPresent(dto -> {
                        WorkFlow workFlow = workFlowRepository.findByCodeAndDeleted(dto.getWorkFlowCategoryDTOList().get(Constants.INT_ZERO).getWorkflowCode(), Constants.LONG_ZERO)
                                .orElse(null);
                        workSheet.setWorkFlow(workFlow);
                    });

            planWsWorkLineDTOS.stream().filter(dto -> dto.getParentCode().equals("ZZ"))
                    .findFirst().ifPresent(dto -> {
                        WorkLine workLine = workLineRepository.findByCodeAndDeleted(dto.getWorkLineCode(), Constants.LONG_ZERO)
                                .orElse(null);
                        workSheet.setWorkLine(workLine).setOrganizationId(Objects.nonNull(workLine)?workLine.getOrganizationId():null);
                    });

        }else if (workSheet.getPedigree().getCode().startsWith("CP")) {

            planWsWorkFlowDtoList.stream().filter(dto -> dto.getParentCode().equals("CP"))
                    .findFirst().ifPresent(dto -> {
                        String workFlowCode  = workSheet.getPedigree().getName().contains("叶轮")?dto.getWorkFlowCategoryDTOList().get(Constants.INT_ZERO).getWorkflowCode():
                                dto.getWorkFlowCategoryDTOList().get(Constants.INT_ONE).getWorkflowCode();
                        WorkFlow workFlow = workFlowRepository.findByCodeAndDeleted(workFlowCode, Constants.LONG_ZERO)
                                .orElse(null);
                        workSheet.setWorkFlow(workFlow);
                    });

            planWsWorkLineDTOS.stream().filter(dto -> dto.getParentCode().equals("CP"))
                    .findFirst().ifPresent(dto -> {
                        WorkLine workLine = workLineRepository.findByCodeAndDeleted(dto.getWorkLineCode(), Constants.LONG_ZERO)
                                .orElse(null);
                        workSheet.setWorkLine(workLine).setOrganizationId(Objects.nonNull(workLine)?workLine.getOrganizationId():null);
                    });
        }
        return workSheetRepository.save(workSheet);
    }

    public void bindWorkSheetSn(List<WorkSheet> workSheets){
        String key = "key_ws_sn"+ LocalDate.now().toString();
        Object o = redisUtils.get(key);
        Integer number = Constants.INT_ZERO;
        if (Objects.nonNull(o)) {
            number = (int) o;
        }
        //获取redis中是否存在 key 如果存在 则将当前key 转为数值，然后往后生产sn号
        //sn号生成的规则 
    }
}
