package net.airuima.ksw.plugin.facility.constant;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Schema(description = "计划排产下单默认初始化工艺路线")
public class PlanWsWorkFlowDTO implements Serializable {

    /**
     * 产品种类编码
     */
    @Schema(description = "产品种类编码")
    private String parentCode;

    /**
     * 产品谱系对应工艺编码
     */
    @Schema(description = "产品谱系对应工艺编码")
    private List<WorkFlowCategoryDTO> workFlowCategoryDTOList;


    public static class WorkFlowCategoryDTO {

        /**
         * 0 含叶轮，1 不含叶轮
         */
        @Schema(description = "0 含叶轮，1 不含叶轮")
        private Integer category;

        /**
         * 工艺路线编码
         */
        @Schema(description = "工艺路线编码")
        private String workflowCode;

        /**
         * 描述
         */
        @Schema(description = "描述")
        private String desc;

        public Integer getCategory() {
            return category;
        }

        public WorkFlowCategoryDTO setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public String getWorkflowCode() {
            return workflowCode;
        }

        public WorkFlowCategoryDTO setWorkflowCode(String workflowCode) {
            this.workflowCode = workflowCode;
            return this;
        }

        public String getDesc() {
            return desc;
        }

        public WorkFlowCategoryDTO setDesc(String desc) {
            this.desc = desc;
            return this;
        }
    }


    public String getParentCode() {
        return parentCode;
    }

    public PlanWsWorkFlowDTO setParentCode(String parentCode) {
        this.parentCode = parentCode;
        return this;
    }

    public List<WorkFlowCategoryDTO> getWorkFlowCategoryDTOList() {
        return workFlowCategoryDTOList;
    }

    public PlanWsWorkFlowDTO setWorkFlowCategoryDTOList(List<WorkFlowCategoryDTO> workFlowCategoryDTOList) {
        this.workFlowCategoryDTOList = workFlowCategoryDTOList;
        return this;
    }
}
