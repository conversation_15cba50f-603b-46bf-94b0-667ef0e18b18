package net.airuima.ksw.plugin.facility.web.rest;

import io.swagger.v3.oas.annotations.Operation;
import net.airuima.ksw.plugin.facility.dto.SyncMaterialDTO;
import net.airuima.ksw.plugin.facility.dto.SyncResultDTO;
import net.airuima.ksw.plugin.facility.service.MouldService;
import net.airuima.util.ResponseData;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 模具对接Resource
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@RestController
@RequestMapping("/plugins/api/moulds")
public class MouldResource {

    private final MouldService mouldService;

    public MouldResource(MouldService mouldService) {
        this.mouldService = mouldService;
    }

    /**
     * 同步模具数据
     *
     * @param syncMaterialDtoList 上传的物料参数列表
     * @return List<SyncResultDTO>
     * <AUTHOR>
     * @date 2021-06-04
     **/
    @Operation(summary = "同步物料、产品型号信息")
    @PostMapping("/syncMoulds")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncMoulds(@RequestBody List<SyncMaterialDTO> syncMaterialDtoList) {
        try {
            return ResponseData.ok(mouldService.syncMoulds(syncMaterialDtoList));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

}
