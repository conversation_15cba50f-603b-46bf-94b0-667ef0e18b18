package net.airuima.ksw.plugin.rbase.service;

import net.airuima.rbase.constant.Constants;
import net.airuima.rbase.constant.ConstantsEnum;
import net.airuima.rbase.constant.MaintainEnum;
import net.airuima.rbase.constant.SnWorkStatusEnum;
import net.airuima.rbase.constant.enums.MaintainHistorySourceEnum;
import net.airuima.rbase.domain.procedure.aps.SubWorkSheet;
import net.airuima.rbase.domain.procedure.aps.WorkSheet;
import net.airuima.rbase.domain.procedure.batch.*;
import net.airuima.rbase.domain.procedure.quality.CheckHistory;
import net.airuima.rbase.domain.procedure.quality.CheckHistoryDetail;
import net.airuima.rbase.domain.procedure.report.StaffPerform;
import net.airuima.rbase.domain.procedure.report.StaffPerformUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnUnqualifiedItem;
import net.airuima.rbase.domain.procedure.single.SnWorkDetail;
import net.airuima.rbase.domain.procedure.single.SnWorkStatus;
import net.airuima.rbase.dto.maintain.MaintainHistoryDTO;
import net.airuima.rbase.dto.quality.SnUnqualifiedDTO;
import net.airuima.rbase.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.rbase.repository.procedure.batch.ContainerDetailUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.batch.WsStepUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformRepository;
import net.airuima.rbase.repository.procedure.report.StaffPerformUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnUnqualifiedItemRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkDetailRepository;
import net.airuima.rbase.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.rbase.service.procedure.quality.CheckHistoryService;
import net.airuima.rbase.service.procedure.quality.api.ICheckHistoryService;
import net.airuima.rbase.service.procedure.scene.NextTodoStepService;
import net.airuima.rbase.util.ValidateUtils;
import net.airuima.util.BeanUtil;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 质检历史结果
 *
 * <AUTHOR>
 * @version 1.8.1
 * @since 1.8.1
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(-1)
public class CheckHistoryAdaptService implements ICheckHistoryService {

    @Autowired
    private NextTodoStepService nextTodoStepService;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SnWorkDetailRepository snWorkDetailRepository;
    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private StaffPerformRepository staffPerformRepository;
    @Autowired
    private StaffPerformUnqualifiedItemRepository staffPerformUnqualifiedItemRepository;
    @Autowired
    private SnUnqualifiedItemRepository snUnqualifiedItemRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private WsStepUnqualifiedItemRepository wsStepUnqualifiedItemRepository;

    @Override
    public void addReleasedInspectUnqualified(List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {
        // 如果检测历史明细列表为空，则直接返回
        if (!ValidateUtils.isValid(checkHistoryDetails)) {
            return;
        }
        //是否为虚拟sn
        boolean virtual = checkHistoryDetails.get(Constants.INT_ZERO).getVirtual();

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();

        //投产粒度工单ID
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();

        List<String> snList = checkHistoryDetails.stream().map(CheckHistoryDetail::getSn).distinct().toList();
        BeanUtil.getBean(CheckHistoryService.class).updateWorkSheetStatus(Objects.nonNull(subWorkSheet), subWorkSheet, workSheet);
        if (ValidateUtils.isValid(snList) && !virtual) {
            List<SnWorkStatus> snWorkStatusList = snWorkStatusRepository.findBySnInAndDeleted(checkHistoryDetails.stream().map(CheckHistoryDetail::getSn).distinct().toList(), Constants.LONG_ZERO);
            List<SnWorkDetail> latestSnWorkDetailList = snWorkStatusList.stream().map(SnWorkStatus::getLatestSnWorkDetail).toList();
            latestSnWorkDetailList.forEach(snWorkDetail -> {
                nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
            });
        } else if (ValidateUtils.isValid(checkHistory.getContainerCode())) {
            Optional<ContainerDetail> containerDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                    containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO) :
                    containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);
            ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));
            nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, containerDetail, Boolean.FALSE);
        } else {
            Optional<BatchWorkDetail> batchWorkDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                    batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
            BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.", "批次详情不存在"));
            nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, batchWorkDetail, Boolean.FALSE);
        }
    }


    @Override
    public void addMaintainInspectUnqualified(ContainerDetail containerDetail, BatchWorkDetail batchWorkDetail, List<SnWorkDetail> snWorkDetailList, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        // 过滤出 存在不良sn
        List<String> snList = checkHistoryDetails.stream()
                .filter(checkHistoryDetail -> !checkHistoryDetail.getResult())
                .toList().stream().map(CheckHistoryDetail::getSn).distinct().toList();
        if (ValidateUtils.isValid(snList)) {
            checkHistoryDetails = checkHistoryDetails.stream()
                    .filter(checkHistoryDetail -> snList.stream().anyMatch(sn -> sn.equals(checkHistoryDetail.getSn()))).collect(Collectors.toList());
        }
        List<SnWorkDetail> qualifiedSnWorkDetailList = null;
        if (!CollectionUtils.isEmpty(snWorkDetailList)) {
            qualifiedSnWorkDetailList = snWorkDetailList.stream().filter(snWorkDetail -> !snList.contains(snWorkDetail.getSn())).collect(Collectors.toList());
        }
        CheckHistoryDetail checkHistoryDetail = checkHistoryDetails.get(Constants.INT_ZERO);
        //是否为虚拟sn
        boolean virtual = checkHistoryDetail.getVirtual();
        CheckHistory checkHistory = checkHistoryDetail.getCheckHistory();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet.getWorkSheet()) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        if (!CollectionUtils.isEmpty(qualifiedSnWorkDetailList)) {
            qualifiedSnWorkDetailList.forEach(snWorkDetail -> {
                nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
            });
        }
        //真实sn，添加开出不良维修记录
        if (!virtual && ValidateUtils.isValid(checkHistoryDetails)) {
            snInspectUnqualified(Boolean.FALSE, checkHistoryDetails, wsStepList);
            if (null != containerDetail) {
                containerInspectUnqualified(virtual, Boolean.FALSE, checkHistoryDetails, wsStepList);
            }
            batchWorkDetailInspectUnqualified(virtual, null != containerDetail, Boolean.FALSE, checkHistoryDetails, wsStepList);
        }
        //解绑容器
        if (!ObjectUtils.isEmpty(containerDetail) && containerDetail.getTransferNumber() == Constants.INT_ZERO && containerDetail.getStatus() == ConstantsEnum.BINDING.getCategoryName()) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
            containerDetailRepository.save(containerDetail);
        }
    }


    /**
     * 单支检测记录处理
     *
     * @param checkHistoryDetails 检测详情列表
     * @return void
     * <AUTHOR>
     * @date 2023/5/15
     */
    public void snInspectUnqualified(boolean release, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        SubWorkSheet subWorkSheet = checkHistory.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet.getWorkSheet()) ? subWorkSheet.getWorkSheet() : checkHistory.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //sn对应的不良 1对1
        List<SnUnqualifiedDTO> snUnqualifiedList = checkHistoryDetails.stream().map(SnUnqualifiedDTO::new).distinct().toList();
        List<SnWorkStatus> snWorkStatuses = snWorkStatusRepository.findBySnInAndDeleted(snUnqualifiedList.stream().map(SnUnqualifiedDTO::getSn).collect(Collectors.toList()), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(snWorkStatuses) || snWorkStatuses.size() != snUnqualifiedList.size()) {
            throw new ResponseException("error.snStatusError", "sn状态异常");
        }
        Map<String, SnWorkStatus> snWorkStatusMap = snWorkStatuses.stream()
                .collect(Collectors.toMap(SnWorkStatus::getSn, Function.identity()));
        List<MaintainHistoryDTO> maintainHistoryList = Lists.newArrayList();
        List<SnUnqualifiedItem> snUnqualifiedItemList = Lists.newArrayList();
        //添加维修分析记录&添加sn不良项目记录
        snUnqualifiedList.forEach(snUnqualifiedDto -> {

            //添加sn不良记录
            SnUnqualifiedItem snUnqualifiedItem = new SnUnqualifiedItem();
            snUnqualifiedItem.setSn(snUnqualifiedDto.getSn()).setSnWorkDetail(snWorkStatusMap.get(snUnqualifiedDto.getSn()).getLatestSnWorkDetail())
                    .setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem())
                    .setStep(checkHistory.getStep())
                    .setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setWorkSheet(checkHistory.getWorkSheet());
            snUnqualifiedItemList.add(snUnqualifiedItem);
        });

        //处理sn状态，修改sn详情
        List<SnWorkStatus> unqualifiedSnWorkStatus = Lists.newArrayList();
        List<SnWorkDetail> unqualifiedSnWorkDetails = Lists.newArrayList();
        snUnqualifiedList.forEach(snUnqualifiedDto -> {
            SnWorkStatus snWorkStatus = snWorkStatusMap.get(snUnqualifiedDto.getSn());
            snWorkStatus.setReworkTime(snWorkStatus.getReworkTime() + Constants.INT_ONE)
                    .setLatestUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem())
                    .setLatestReworkSnWorkDetail(snWorkStatus.getLatestSnWorkDetail())
                    .setIsUpdateBatchWorkDetail(Boolean.TRUE)
                    .setStatus(SnWorkStatusEnum.SCRAP.getStatus());

            //修改sn详情为不合格
            SnWorkDetail latestSnWorkDetail = snWorkStatus.getLatestSnWorkDetail();
            latestSnWorkDetail.setResult(Constants.INT_ZERO).setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem());
            StaffPerform staffPerform = staffPerformRepository.findBySnWorkDetailIdAndDeleted(latestSnWorkDetail.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(null);
            if (null != staffPerform) {
                staffPerformRepository.save(staffPerform.setQualifiedNumber(net.airuima.constant.Constants.INT_ZERO).setUnqualifiedNumber(net.airuima.constant.Constants.INT_ONE));
                List<StaffPerformUnqualifiedItem> staffPerformUnqualifiedItems = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndDeleted(staffPerform.getId(), net.airuima.constant.Constants.LONG_ZERO);
                if (CollectionUtils.isEmpty(staffPerformUnqualifiedItems)) {
                    StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = new StaffPerformUnqualifiedItem().setStaffPerform(staffPerform)
                            .setUnqualifiedItem(snUnqualifiedDto.getUnqualifiedItem())
                            .setNumber(net.airuima.constant.Constants.INT_ONE).setRecordDate(staffPerform.getRecordDate()).setRecordTime(staffPerform.getRecordTime());
                    staffPerformUnqualifiedItem.setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                    staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                }
            }
            unqualifiedSnWorkStatus.add(snWorkStatus);
            unqualifiedSnWorkDetails.add(latestSnWorkDetail);
        });
        //保存sn相关数据
        snWorkStatusRepository.saveAll(unqualifiedSnWorkStatus);
        List<SnWorkDetail> snWorkDetailList = snWorkDetailRepository.saveAll(unqualifiedSnWorkDetails);
        snUnqualifiedItemRepository.saveAll(snUnqualifiedItemList);

        //更新Rworker下个可能的待做工序信息
        snWorkDetailList.forEach(snWorkDetail -> {
            if (CollectionUtils.isEmpty(maintainHistoryList) || !maintainHistoryList.stream().noneMatch(maintainHistory -> maintainHistory.getSnWorkStatus().getSn().equals(snWorkDetail.getSn()))) {
                nextTodoStepService.updateNextStepTodoInfoWhenSingleStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, snWorkDetail, snWorkDetail.getReworkTime(), Boolean.FALSE, Boolean.FALSE);
            }
        });
    }

    public void containerInspectUnqualified(boolean virtual, boolean release, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();
        //sn对应的不良 1对1
        List<SnUnqualifiedDTO> snUnqualifiedDtos = checkHistoryDetails.stream().map(SnUnqualifiedDTO::new).distinct().toList();
        Optional<ContainerDetail> containerDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                containerDetailRepository.findByBatchWorkDetailSubWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO) :
                containerDetailRepository.findByBatchWorkDetailWorkSheetIdAndBatchWorkDetailStepIdAndContainerCodeAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), checkHistory.getContainerCode(), Constants.LONG_ZERO);
        ContainerDetail containerDetail = containerDetailOptional.orElseThrow(() -> new ResponseException("error.", "容器详情不存在"));

        //保存容器不良信息
        ContainerDetail finalContainerDetail = containerDetail;
        snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach(((unqualifiedItem, snUnqualifieds) -> {
            ContainerDetailUnqualifiedItem containerDetailUnqualifiedItem = containerDetailUnqualifiedItemRepository.findByContainerDetailIdAndUnqualifiedItemIdAndDeleted(finalContainerDetail.getId(), unqualifiedItem.getId(), Constants.LONG_ZERO).orElse(new ContainerDetailUnqualifiedItem());
            containerDetailUnqualifiedItem.setContainerDetail(finalContainerDetail).setUnqualifiedItem(unqualifiedItem).setNumber(containerDetailUnqualifiedItem.getNumber() + snUnqualifieds.size()).setDeleted(Constants.LONG_ZERO);
            containerDetailUnqualifiedItemRepository.save(containerDetailUnqualifiedItem);
        }));

        //调整容器详情信息
        if (snUnqualifiedDtos.size() == containerDetail.getTransferNumber()) {
            containerDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(containerDetail.getInputNumber()).setUnbindTime(LocalDateTime.now()).setStatus(ConstantsEnum.UNBIND.getCategoryName());
        } else {
            containerDetail.setTransferNumber(containerDetail.getTransferNumber() > Constants.INT_ZERO ? containerDetail.getTransferNumber() - snUnqualifiedDtos.size() : Constants.INT_ZERO)
                    .setQualifiedNumber(containerDetail.getQualifiedNumber() - snUnqualifiedDtos.size())
                    .setUnqualifiedNumber(containerDetail.getUnqualifiedNumber() + snUnqualifiedDtos.size());
        }
        //如果是虚拟的SN则需要以容器工序不良更新员工产量及不良数据
        if (virtual) {
            List<StaffPerform> staffPerforms = staffPerformRepository.findByContainerDetailIdAndDeleted(containerDetail.getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(staffPerforms)) {
                staffPerforms.forEach(staffPerform -> {
                    staffPerform = staffPerformRepository.save(staffPerform.setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() + snUnqualifiedDtos.size()).setQualifiedNumber(staffPerform.getQualifiedNumber() - snUnqualifiedDtos.size()));
                    StaffPerform finalStaffPerform = staffPerform;
                    snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach(((unqualifiedItem, snUnqualifieds) -> {
                        StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(finalStaffPerform.getId(), unqualifiedItem.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                        staffPerformUnqualifiedItem.setStaffPerform(finalStaffPerform).setUnqualifiedItem(unqualifiedItem)
                                .setRecordDate(finalStaffPerform.getRecordDate()).setRecordTime(finalStaffPerform.getRecordTime())
                                .setNumber(staffPerformUnqualifiedItem.getNumber() + snUnqualifieds.size())
                                .setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                        staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                    }));
                });
            }
        }
        containerDetail = containerDetailRepository.save(containerDetail);
        SubWorkSheet subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : containerDetail.getBatchWorkDetail().getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //更新Rworker下个可能的的待做工序信息
        if (virtual) {
            nextTodoStepService.updateNextTodoStepInfoWhenContainerStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, containerDetail, Boolean.FALSE);
        }
    }

    /**
     * 批次检测记录处理
     *
     * @param checkHistoryDetails 检测详情列表
     * @return void
     * <AUTHOR>
     * @date 2023/5/15
     */
    public void batchWorkDetailInspectUnqualified(boolean virtual, boolean existContainer, boolean release, List<CheckHistoryDetail> checkHistoryDetails, List<WsStep> wsStepList) {

        CheckHistory checkHistory = checkHistoryDetails.get(Constants.INT_ZERO).getCheckHistory();

        //sn对应的不良 1对1
        List<SnUnqualifiedDTO> snUnqualifiedDtos = checkHistoryDetails.stream().map(SnUnqualifiedDTO::new).distinct().toList();

        //获取批次信息
        Optional<BatchWorkDetail> batchWorkDetailOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ? batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO) :
                batchWorkDetailRepository.findByWorkSheetIdAndStepIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), Constants.LONG_ZERO);
        BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.orElseThrow(() -> new ResponseException("error.", "批次详情不存在"));
        SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
        WorkSheet workSheet = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet() : batchWorkDetail.getWorkSheet();
        Long productWorkSheetId = Objects.nonNull(subWorkSheet) ? subWorkSheet.getWorkSheet().getId() : checkHistory.getWorkSheet().getId();
        //保存批次不良详情
        snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach((key, value) -> {
            Optional<WsStepUnqualifiedItem> wsStepUnqualifiedItemOptional = !ObjectUtils.isEmpty(checkHistory.getSubWorkSheet()) ?
                    wsStepUnqualifiedItemRepository.findBySubWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getSubWorkSheet().getId(), checkHistory.getStep().getId(), key.getId(), Constants.LONG_ZERO) :
                    wsStepUnqualifiedItemRepository.findByWorkSheetIdAndStepIdAndUnqualifiedItemIdAndDeleted(checkHistory.getWorkSheet().getId(), checkHistory.getStep().getId(), key.getId(), Constants.LONG_ZERO);
            WsStepUnqualifiedItem wsStepUnqualifiedItem = wsStepUnqualifiedItemOptional.orElse(new WsStepUnqualifiedItem());
            wsStepUnqualifiedItem.setWorkSheet(checkHistory.getWorkSheet()).setSubWorkSheet(checkHistory.getSubWorkSheet())
                    .setStep(checkHistory.getStep()).setUnqualifiedItem(key).setNumber(wsStepUnqualifiedItem.getNumber() + value.size())
                    .setFlag(ObjectUtils.isEmpty(wsStepUnqualifiedItem.getFlag()) ? Boolean.FALSE : wsStepUnqualifiedItem.getFlag())
                    .setRecordDate(LocalDate.now()).setDeleted(Constants.LONG_ZERO);
            if (Objects.isNull(wsStepUnqualifiedItem.getOperatorId())) {
                wsStepUnqualifiedItem.setOperatorId(checkHistory.getOperatorId());
            }
            wsStepUnqualifiedItemRepository.save(wsStepUnqualifiedItem);
        });
        //虚拟SN以及不存在容器时以批量工序开出来更新员工产量及不良数据
        if (virtual && !existContainer) {
            List<StaffPerform> staffPerformList = staffPerformRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetail.getId(), net.airuima.constant.Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(staffPerformList)) {
                staffPerformList.forEach(staffPerform -> {
                    staffPerform = staffPerformRepository.save(staffPerform.setUnqualifiedNumber(staffPerform.getUnqualifiedNumber() + snUnqualifiedDtos.size()).setQualifiedNumber(staffPerform.getQualifiedNumber() - snUnqualifiedDtos.size()));
                    StaffPerform finalStaffPerform = staffPerform;
                    snUnqualifiedDtos.stream().collect(Collectors.groupingBy(SnUnqualifiedDTO::getUnqualifiedItem)).forEach(((unqualifiedItem, snUnqualifieds) -> {
                        StaffPerformUnqualifiedItem staffPerformUnqualifiedItem = staffPerformUnqualifiedItemRepository.findByStaffPerformIdAndUnqualifiedItemIdAndDeleted(finalStaffPerform.getId(), unqualifiedItem.getId(), net.airuima.constant.Constants.LONG_ZERO).orElse(new StaffPerformUnqualifiedItem());
                        staffPerformUnqualifiedItem.setStaffPerform(finalStaffPerform)
                                .setRecordDate(finalStaffPerform.getRecordDate()).setRecordTime(finalStaffPerform.getRecordTime())
                                .setUnqualifiedItem(unqualifiedItem).setNumber(staffPerformUnqualifiedItem.getNumber() + snUnqualifieds.size())
                                .setDeleted(net.airuima.constant.Constants.LONG_ZERO);
                        staffPerformUnqualifiedItemRepository.save(staffPerformUnqualifiedItem);
                    }));
                });
            }
        }
        //原始合格数
        int originQualifiedNumber = batchWorkDetail.getQualifiedNumber();
        //原始不合格数
        int originUnqualifiedNumber = batchWorkDetail.getUnqualifiedNumber();
        //原始待流转数
        int originTransferNumber = batchWorkDetail.getTransferNumber();
        //修改批次详情信息
        if (snUnqualifiedDtos.size() == batchWorkDetail.getTransferNumber()) {
            //todo: 可能需要后续补充
            batchWorkDetail.setTransferNumber(Constants.INT_ZERO).setQualifiedNumber(Constants.INT_ZERO).setUnqualifiedNumber(batchWorkDetail.getInputNumber());
        } else {
            batchWorkDetail.setTransferNumber(batchWorkDetail.getTransferNumber() - snUnqualifiedDtos.size())
                    .setQualifiedNumber(batchWorkDetail.getQualifiedNumber() - snUnqualifiedDtos.size())
                    .setUnqualifiedNumber(batchWorkDetail.getUnqualifiedNumber() + snUnqualifiedDtos.size());
        }
        batchWorkDetail = batchWorkDetailRepository.save(batchWorkDetail);
        //更新Rworker下个可能的待做工序信息
        if (virtual && !existContainer) {
            nextTodoStepService.updateNextTodoStepInfoWhenBatchStep(productWorkSheetId, workSheet, subWorkSheet, wsStepList, batchWorkDetail, Boolean.FALSE);
        }
        //若为最后一个工序则可能需要更新工单数据
        BeanUtil.getBean(CheckHistoryService.class).updateWorkSheetReleaseWhenLastStep(batchWorkDetail, originQualifiedNumber, originUnqualifiedNumber, originTransferNumber);
    }
}
